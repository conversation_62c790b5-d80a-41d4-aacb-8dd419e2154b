import { inject } from '@adonisjs/core'
import ZnAppointment, { EAppointmentStatus } from '#models/store_service/zn_appointment'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreTax from '#models/store_service/zn_store_tax'
import { DateTime } from 'luxon'
import { v4 as uuidv4 } from 'uuid'

@inject()
export default class AppointmentService {
  async create(data: any) {
    const {
      storeId,
      customerId,
      startTime,
      endTime,
      notes,
      taxId,
      services = [],
      packages = [],
    } = data

    const appointment = await ZnAppointment.create({
      storeId,
      customerId,
      status: EAppointmentStatus.BOOKED,
      startTime: startTime
        ? (DateTime.fromJSDate(new Date(startTime)).toUTC().toJSDate() as any)
        : undefined,
      endTime: endTime
        ? (DateTime.fromJSDate(new Date(endTime)).toUTC().toJSDate() as any)
        : undefined,
      notes,
      taxId,
      subtotal: 0,
      discount: 0,
      taxAmount: 0,
      tipAmount: 0,
      total: 0,
    })

    if (services.length > 0) {
      const storeServices = await ZnStoreService.query()
        .whereIn('id', services)
        .where('storeId', storeId)

      for (const service of storeServices) {
        await appointment.related('services').sync({
          [service.id]: {
            id: uuidv4(),
            price: service.price,
            duration: service.duration,
          },
        })
      }
    }

    if (packages.length > 0) {
      const storePackages = await ZnStorePackage.query()
        .whereIn('id', packages)
        .where('storeId', storeId)

      for (const pkg of storePackages) {
        const inputPackage = packages.find((p: any) => p.id === pkg.id)
        const packagePrice = inputPackage && inputPackage.price ? inputPackage.price : 0

        await appointment.related('packages').sync({
          [pkg.id]: {
            id: uuidv4(),
            price: packagePrice,
          },
        })
      }
    }

    await this.calculateTotals(appointment.id)

    return this.findById(appointment.id)
  }

  async findById(id: string) {
    return ZnAppointment.query()
      .where('id', id)
      .preload('store')
      .preload('customer')
      .preload('tax')
      .preload('services')
      .preload('packages')
      .firstOrFail()
  }

  async getByStore(storeId: string, params: any = {}) {
    const { page = 1, limit = 10, status, startDate, endDate, customerId } = params

    const query = ZnAppointment.query()
      .where('storeId', storeId)
      .preload('customer')
      .preload('services')
      .preload('packages')

    if (status) {
      query.where('status', status)
    }

    if (startDate && endDate) {
      query
        .where(
          'startTime',
          '>=',
          startDate
            ? (DateTime.fromJSDate(new Date(startDate)).toUTC().startOf('day').toJSDate() as any)
            : undefined
        )
        .where(
          'endTime',
          '<=',
          endDate
            ? (DateTime.fromJSDate(new Date(endDate)).toUTC().endOf('day').toJSDate() as any)
            : undefined
        )
    }

    if (customerId) {
      query.where('customerId', customerId)
    }

    return query.orderBy('startTime', 'desc').paginate(page, limit)
  }

  async getByCustomer(customerId: string, params: any = {}) {
    const { page = 1, limit = 10, status, storeId } = params

    const query = ZnAppointment.query()
      .where('customerId', customerId)
      .preload('store')
      .preload('services')
      .preload('packages')

    if (status) {
      query.where('status', status)
    }

    if (storeId) {
      query.where('storeId', storeId)
    }

    return query.orderBy('startTime', 'desc').paginate(page, limit)
  }

  async update(id: string, data: any) {
    const appointment = await this.findById(id)
    const { storeId, customerId, status, startTime, endTime, notes, taxId, services, packages } =
      data

    if (storeId) appointment.storeId = storeId
    if (customerId) appointment.customerId = customerId
    if (status) appointment.status = status
    if (startTime)
      appointment.startTime = startTime
        ? (DateTime.fromJSDate(new Date(startTime)).toUTC().toJSDate() as any)
        : undefined
    if (endTime)
      appointment.endTime = endTime
        ? (DateTime.fromJSDate(new Date(endTime)).toUTC().toJSDate() as any)
        : undefined
    if (notes !== undefined) appointment.notes = notes
    if (taxId !== undefined) appointment.taxId = taxId

    await appointment.save()

    if (services) {
      if (services.length > 0) {
        const storeServices = await ZnStoreService.query()
          .whereIn('id', services)
          .where('storeId', appointment.storeId)

        for (const service of storeServices) {
          await appointment.related('services').sync({
            [service.id]: {
              id: uuidv4(),
              price: service.price,
              duration: service.duration,
            },
          })
        }
      }
    }

    if (packages) {
      if (packages.length > 0) {
        const storePackages = await ZnStorePackage.query()
          .whereIn('id', packages)
          .where('storeId', appointment.storeId)

        for (const pkg of storePackages) {
          const inputPackage = packages.find((p: any) => p.id === pkg.id)
          const packagePrice = inputPackage && inputPackage.price ? inputPackage.price : 0

          await appointment.related('packages').sync({
            [pkg.id]: {
              id: uuidv4(),
              price: packagePrice,
            },
          })
        }
      }
    }

    if (services || packages || taxId !== undefined) {
      await this.calculateTotals(id)
    }

    return this.findById(id)
  }

  async updateStatus(id: string, status: EAppointmentStatus) {
    const appointment = await this.findById(id)
    appointment.status = status
    await appointment.save()
    return appointment
  }

  async delete(id: string) {
    const appointment = await this.findById(id)
    await appointment.delete()
    return { success: true }
  }

  async calculateTotals(id: string) {
    const appointment = await this.findById(id)
    let subtotal = 0

    const services = await appointment.related('services').query()
    for (const service of services) {
      const servicePrice =
        typeof service.$extras.pivot_price === 'number'
          ? service.$extras.pivot_price
          : parseFloat(service.$extras.pivot_price || '0')
      subtotal += servicePrice || 0
    }

    const packages = await appointment.related('packages').query()
    for (const pkg of packages) {
      const packagePrice =
        typeof pkg.$extras.pivot_price === 'number'
          ? pkg.$extras.pivot_price
          : parseFloat(pkg.$extras.pivot_price || '0')
      subtotal += packagePrice || 0
    }

    let taxAmount = 0
    if (appointment.taxId) {
      const tax = await ZnStoreTax.find(appointment.taxId as string)
      if (tax) {
        taxAmount = (subtotal * tax.value) / 100
      }
    }

    const roundedSubtotal = parseFloat(subtotal.toFixed(2))
    const roundedTaxAmount = parseFloat(taxAmount.toFixed(2))
    const roundedTotal = parseFloat((roundedSubtotal + roundedTaxAmount).toFixed(2))

    appointment.subtotal = roundedSubtotal
    appointment.taxAmount = roundedTaxAmount
    appointment.total = roundedTotal

    await appointment.save()
    return appointment
  }

  /**
   * Get the next available day for booking
   * @param storeId string
   * @param serviceIds string[]
   * @param packageIds string[]
   * @param fromDate string (ISO, optional)
   * @returns string | null (YYYY-MM-DD or null if not found in 30 days)
   */
  async getNextAvailableDay({
    storeId,
    serviceIds = [],
    packageIds = [],
    fromDate,
  }: {
    storeId: string
    serviceIds?: string[]
    packageIds?: string[]
    fromDate?: string
  }): Promise<string | null> {
    // 1. Fetch store info (working hours, timezone)
    const store = await (await import('#models/zn_store')).default.findOrFail(storeId)
    const workingHoursRaw = store.workingHour
    const timezone = store.timezone || 'UTC'
    let workingHours: any[] = []
    try {
      workingHours =
        typeof workingHoursRaw === 'string' ? JSON.parse(workingHoursRaw) : workingHoursRaw
    } catch {
      workingHours = []
    }

    // 2. Calculate total duration (in minutes)
    let totalDuration = 0
    if (serviceIds.length > 0) {
      const services = await (await import('#models/store_service/zn_store_service')).default
        .query()
        .whereIn('id', serviceIds)
      totalDuration += services.reduce((sum, s) => sum + (s.duration || 0), 0)
    }
    if (packageIds.length > 0) {
      const ZnStorePackage = (await import('#models/store_service/zn_store_package')).default
      for (const packageId of packageIds) {
        const pkg = await ZnStorePackage.findOrFail(packageId)
        await pkg.load('services')
        totalDuration += pkg.services.reduce((sum: number, s: any) => sum + (s.duration || 0), 0)
      }
    }
    if (totalDuration === 0) return null

    // 3. Start searching from fromDate (or today)
    let searchDate = fromDate
      ? DateTime.fromISO(fromDate, { zone: timezone })
      : DateTime.now().setZone(timezone)
    searchDate = searchDate.startOf('day')
    const maxDays = 30
    for (let i = 0; i < maxDays; i++) {
      const dayOfWeek = searchDate.weekday % 7 // 0=Sunday, 1=Monday, ...
      const dayName = searchDate.toFormat('cccc') // e.g., 'Monday'
      // Find working hour for this day
      const wh = workingHours.find((h: any) => h.name?.toLowerCase() === dayName.toLowerCase())
      if (!wh || !wh.isOpen) {
        searchDate = searchDate.plus({ days: 1 })
        continue
      }
      // Parse open/close time
      const open = DateTime.fromFormat(wh.from, 'HH:mm', { zone: timezone })
      const close = DateTime.fromFormat(wh.to, 'HH:mm', { zone: timezone })
      if (!open.isValid || !close.isValid) {
        searchDate = searchDate.plus({ days: 1 })
        continue
      }
      // 4. Fetch all appointments for this day
      const dayStart = searchDate.set({
        hour: open.hour,
        minute: open.minute,
        second: 0,
        millisecond: 0,
      })
      const dayEnd = searchDate.set({
        hour: close.hour,
        minute: close.minute,
        second: 0,
        millisecond: 0,
      })
      const appointments = await (
        await import('#models/store_service/zn_appointment')
      ).default
        .query()
        .where('storeId', storeId)
        .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
        .where('startTime', '>=', dayStart.toUTC().toISO() || '')
        .where('endTime', '<=', dayEnd.toUTC().toISO() || '')
        .orderBy('startTime', 'asc')

      // 5. Find a free slot that fits totalDuration
      let slotStart = dayStart
      let slotEnd = slotStart.plus({ minutes: totalDuration })
      let found = false
      while (slotEnd <= dayEnd) {
        // Check for overlap
        const overlap = appointments.some((appt: any) => {
          const apptStart = DateTime.fromJSDate(appt.startTime, { zone: timezone })
          const apptEnd = DateTime.fromJSDate(appt.endTime, { zone: timezone })
          return apptEnd > slotStart && apptStart < slotEnd
        })
        if (!overlap) {
          found = true
          break
        }
        slotStart = slotStart.plus({ minutes: 15 }) // increment by 15 min
        slotEnd = slotStart.plus({ minutes: totalDuration })
      }
      if (found) {
        return searchDate.toISODate()
      }
      searchDate = searchDate.plus({ days: 1 })
    }
    return null
  }

  /**
   * Get available time slots for a specific date
   * @param storeId string
   * @param serviceIds string[]
   * @param packageIds string[]
   * @param date string (YYYY-MM-DD)
   * @returns Array of available time slots
   */
  async getAvailableSlots({
    storeId,
    serviceIds = [],
    packageIds = [],
    date,
  }: {
    storeId: string
    serviceIds?: string[]
    packageIds?: string[]
    date: string
  }): Promise<{ startTime: string; endTime: string }[]> {
    // 1. Fetch store info (working hours, timezone)
    const store = await (await import('#models/zn_store')).default.findOrFail(storeId)
    const workingHoursRaw = store.workingHour
    const timezone = store.timezone || 'UTC'
    let workingHours: any[] = []
    try {
      workingHours =
        typeof workingHoursRaw === 'string' ? JSON.parse(workingHoursRaw) : workingHoursRaw
    } catch {
      workingHours = []
    }

    // 2. Calculate total duration (in minutes)
    let totalDuration = 0
    if (serviceIds.length > 0) {
      const services = await (await import('#models/store_service/zn_store_service')).default
        .query()
        .whereIn('id', serviceIds)
      totalDuration += services.reduce((sum, s) => sum + (s.duration || 0), 0)
    }
    if (packageIds.length > 0) {
      const ZnStorePackage = (await import('#models/store_service/zn_store_package')).default
      for (const packageId of packageIds) {
        const pkg = await ZnStorePackage.findOrFail(packageId)
        await pkg.load('services')
        totalDuration += pkg.services.reduce((sum: number, s: any) => sum + (s.duration || 0), 0)
      }
    }
    if (totalDuration === 0) return []

    // 3. Parse the requested date
    const searchDate = DateTime.fromISO(date, { zone: timezone }).startOf('day')
    if (!searchDate.isValid) return []

    const dayName = searchDate.toFormat('cccc') // e.g., 'Monday'

    // 4. Find working hour for this day
    const wh = workingHours.find((h: any) => h.name?.toLowerCase() === dayName.toLowerCase())
    if (!wh || !wh.isOpen) {
      return []
    }

    // 5. Parse open/close time
    const open = DateTime.fromFormat(wh.from, 'HH:mm', { zone: timezone })
    const close = DateTime.fromFormat(wh.to, 'HH:mm', { zone: timezone })
    if (!open.isValid || !close.isValid) {
      return []
    }

    // 6. Fetch all appointments for this day
    const dayStart = searchDate.set({
      hour: open.hour,
      minute: open.minute,
      second: 0,
      millisecond: 0,
    })
    const dayEnd = searchDate.set({
      hour: close.hour,
      minute: close.minute,
      second: 0,
      millisecond: 0,
    })

    const appointments = await (
      await import('#models/store_service/zn_appointment')
    ).default
      .query()
      .where('storeId', storeId)
      .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
      .where('startTime', '>=', dayStart.toUTC().toISO() || '')
      .where('startTime', '<', dayEnd.toUTC().toISO() || '')
      .orderBy('startTime', 'asc')

    // Debug logging
    console.log('Debug - Available slots query:')
    console.log('Store ID:', storeId)
    console.log('Date:', date)
    console.log('Day start (UTC):', dayStart.toUTC().toISO())
    console.log('Day end (UTC):', dayEnd.toUTC().toISO())
    console.log('Found appointments:', appointments.length)
    appointments.forEach((appt: any) => {
      console.log(`Appointment: ${appt.id} - ${appt.startTime} to ${appt.endTime}`)
    })

    // 7. Generate all possible time slots (15-minute intervals)
    const availableSlots: { startTime: string; endTime: string }[] = []
    let slotStart = dayStart
    let slotEnd = slotStart.plus({ minutes: totalDuration })

    while (slotEnd <= dayEnd) {
      // Check for overlap with existing appointments
      const overlap = appointments.some((appt: any) => {
        // Convert appointment times from UTC to store timezone
        const apptStart = DateTime.fromJSDate(appt.startTime).setZone(timezone)
        const apptEnd = DateTime.fromJSDate(appt.endTime).setZone(timezone)
        // Check if there's any overlap: appointment ends after slot starts AND appointment starts before slot ends
        return apptEnd > slotStart && apptStart < slotEnd
      })

      if (!overlap) {
        availableSlots.push({
          startTime: slotStart.toISO() || '',
          endTime: slotEnd.toISO() || '',
        })
      }

      // Move to next 15-minute slot
      slotStart = slotStart.plus({ minutes: 15 })
      slotEnd = slotStart.plus({ minutes: totalDuration })
    }

    return availableSlots
  }
}
