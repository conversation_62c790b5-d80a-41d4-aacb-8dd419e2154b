import { BaseCommand } from '@adonisjs/core/ace'
import ZnStore from './app/models/zn_store.js'
import { v4 as uuidv4 } from 'uuid'

// Simple script to check for stores and create a test store if needed
async function setupTestStore() {
  try {
    // Check for existing stores
    const existingStores = await ZnStore.query().limit(5)
    console.log('Existing stores:', existingStores.length)
    
    if (existingStores.length > 0) {
      const store = existingStores[0]
      console.log('Using existing store:', {
        id: store.id,
        name: store.name,
        workingHour: store.workingHour,
        timezone: store.timezone
      })
      return store.id
    }
    
    // Create a test store with working hours
    const workingHours = [
      { name: 'Monday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Tuesday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Wednesday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Thursday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Friday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Saturday', from: '10:00', to: '16:00', isOpen: true },
      { name: 'Sunday', from: '10:00', to: '16:00', isOpen: false }
    ]
    
    const testStore = await ZnStore.create({
      id: uuidv4(),
      name: 'Test Nail Salon',
      address: '123 Test Street, Test City',
      phoneNumber: '+1234567890',
      email: '<EMAIL>',
      website: 'https://testsalon.com',
      workingHour: JSON.stringify(workingHours),
      timezone: 'America/New_York',
      userId: uuidv4(), // Generate a random user ID for testing
      isZurno: true,
      verified: true,
      isManageBookingEnabled: true
    })
    
    console.log('Created test store:', {
      id: testStore.id,
      name: testStore.name,
      workingHour: testStore.workingHour,
      timezone: testStore.timezone
    })
    
    return testStore.id
  } catch (error) {
    console.error('Error setting up test store:', error)
    throw error
  }
}

// Run the setup
setupTestStore().then(storeId => {
  console.log('Test store ID:', storeId)
  process.exit(0)
}).catch(error => {
  console.error('Failed to setup test store:', error)
  process.exit(1)
})
