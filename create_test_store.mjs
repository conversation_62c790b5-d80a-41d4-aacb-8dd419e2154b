import { createRequire } from 'module';
const require = createRequire(import.meta.url);

// Simple script to create a test store
async function createTestStore() {
  try {
    // Use curl to create a store via API
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    
    const workingHours = [
      { name: 'Monday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Tuesday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Wednesday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Thursday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Friday', from: '09:00', to: '18:00', isOpen: true },
      { name: 'Saturday', from: '10:00', to: '16:00', isOpen: true },
      { name: 'Sunday', from: '10:00', to: '16:00', isOpen: false }
    ];
    
    const storeData = {
      name: 'Test Nail Salon',
      address: '123 Test Street, Test City',
      phoneNumber: '+1234567890',
      email: '<EMAIL>',
      website: 'https://testsalon.com',
      workingHour: workingHours,
      timezone: 'America/New_York',
      zipCode: '12345'
    };
    
    const curlCommand = `curl -X POST "http://localhost:3333/v1/stores" \\
      -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5YzA2OTc1Mi0yZWIxLTQ1MzktOGU2Zi04Mjk3Yzg0ZjZlZTMiLCJpYXQiOjE3NTE5NjQ0OTR9.xMy4OGUT8CH439Zt6fHobAkykB31Te_IVlJL7a2mJ6g" \\
      -H "Content-Type: application/json" \\
      -d '${JSON.stringify(storeData)}'`;
    
    console.log('Creating test store...');
    const result = await execAsync(curlCommand);
    console.log('Store creation result:', result.stdout);
    
    if (result.stderr) {
      console.error('Error:', result.stderr);
    }
    
  } catch (error) {
    console.error('Failed to create test store:', error);
  }
}

createTestStore();
